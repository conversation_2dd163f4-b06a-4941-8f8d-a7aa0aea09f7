<?php $__env->startSection('content'); ?>
    <div class="container-fluid flex-grow-1 container-p-y">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="head-label text-center">
                    <h5 class="card-title mb-0"><b><?php echo e($menuName); ?></b></h5>
                </div>
                <?php if($permissions['canAdd']): ?>
                    <button id="btnAdd" type="submit" class="btn btn-primary waves-effect waves-light"
                        onClick="fnAddEdit(this, '<?php echo e(url('/client/create')); ?>', 0, 'Add Solar Application',true)">
                        <span class="tf-icons mdi mdi-plus">&nbsp;</span>Adds New Solar Application
                    </button>
                <?php endif; ?>
            </div>
            <div class="col-12 d-flex justify-content-between align-items-center">
                
            </div>
            <div class="card-datatable text-nowrap">
                <table id="grid" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Action</th>
                            <th>Customer Name</th>
                            <th>Mobile</th>
                            <th>Age</th>
                            <th>Quotation Status</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            initializeDataTable();
        });

        // $('#btnExcel').click(function() {
        //     $('#grid').DataTable().button('.buttons-excel').trigger();
        // });
        // $('#btnCsv').click(function() {
        //     $('#grid').DataTable().button('.buttons-csv').trigger();
        // });
        // $('#btnPdf').click(function() {
        //     $('#grid').DataTable().button('.buttons-pdf').trigger();
        // });

        function initializeDataTable() {
            $("#grid").DataTable({
                buttons: [{
                        extend: 'excelHtml5',
                        title: 'Client Report',
                        className: 'buttons-excel d-none',
                        exportOptions: {
                            columns: [1, 2, 3]
                        }
                    },
                    {
                        extend: 'csvHtml5',
                        title: 'Client Report',
                        className: 'buttons-csv d-none',
                        exportOptions: {
                            columns: [1, 2, 3]
                        }
                    },
                    {
                        extend: 'pdfHtml5',
                        title: 'Client Report',
                        className: 'buttons-pdf d-none',
                        exportOptions: {
                            columns: [1, 2, 3]
                        }
                    }
                ],
                responsive: true,
                autoWidth: false,
                serverSide: false,
                processing: true,
                'language': {
                    "loadingRecords": "&nbsp;",
                    "processing": "<img src='<?php echo e(asset('assets/img/illustrations/loader.gif')); ?>' alt='loader' />"
                },
                order: [
                    [1, "asc"]
                ],
                ajax: {
                    url: "<?php echo e(config('apiConstants.CLIENT_URLS.CLIENT')); ?>",
                    type: "GET",
                    headers: {
                        Authorization: "Bearer " + getCookie("access_token"),
                    },
                },
                columns: [{
                        data: "id",
                        orderable: false,
                        render: function(data, type, row) {
                            var html = "<ul class='list-inline m-0'>";

                            // Edit Button (This is your existing edit button logic)
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canEdit']); ?>,
                                    "<?php echo e(url('/client/create')); ?>", "Edit",
                                    data, "Edit Solar Application", true) +
                                "</li>";

                            // Delete Button
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canDelete']); ?>,
                                    "fnShowConfirmDeleteDialog('" + row.customer_name +
                                    "',fnDeleteRecord," +
                                    data + ",'" +
                                    '<?php echo e(config('apiConstants.USER_API_URLS.USER_DELETE')); ?>' +
                                    "','#grid')", "Delete") +
                                "</li>";

                            // Accept Button (Extra Menu)
                            html += "<li class='list-inline-item'>" +
                                "<button type='button' onclick='acceptcustomer(" + data +
                                ")' class='btn btn-sm btn-success' title='Accept'>" +
                                "<i class='mdi mdi-check-circle'></i>" +
                                "</button>" +
                                "</li>";

                            html += "</ul>";
                            return html;
                        },
                    },
                    {
                        data: "customer_name",
                    },
                    {
                        data: "mobile",
                    },
                    {
                        data: "age",
                    },
                    {
                        data: "status",
                    }

                ]
            });
        }

        function acceptcustomer(id) {
            var Url = "<?php echo e(config('apiConstants.CLIENT_URLS.CLIENT_ACCEPT')); ?>";

            var postData = {
                id: id,
            };

            fnCallAjaxHttpPostEvent(Url, postData, true, true, function(response) {
                if (response.status === 200) {
                    $('#grid').DataTable().ajax.reload();
                    ShowMsg("bg-success", response.message);
                } else {
                    ShowMsg("bg-warning", 'The record could not be processed.');
                }
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\INCT\Parth\practice projects\RJ_ENERGY\resources\views/client/client_index.blade.php ENDPATH**/ ?>