<aside id="layout-menu" class="layout-menu-horizontal menu-horizontal menu bg-menu-theme flex-grow-0"
    style="border-top: 1px solid #0000001a;">
    <div class="container-fluid d-flex h-100">
        <a href="#" class="menu-horizontal-prev d-none"></a>
        <div class="menu-horizontal-wrapper">
            <ul class="menu-inner" style="margin-left: 0px;">
                <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($menu['display_in_menu'] == 1 && $menu['parent_menu_id'] == 0): ?>
                        <?php
                            // Check if there are child menus for the current menu item
                            $childMenus = collect($menus)->where('parent_menu_id', $menu['id']);
                            $hasSubMenu = $childMenus->isNotEmpty();
                            // Check if this menu or any of its child menus are active
                            $isActive =
                                request()->is(ltrim($menu['navigation_url'], '/')) ||
                                $childMenus->contains(function ($childMenu) {
                                    return request()->is(ltrim($childMenu['navigation_url'], '/'));
                                });
                        ?>

                        <li class="menu-item <?php echo e($isActive ? 'active' : ''); ?>">
                            <a href="<?php echo e(url($menu['navigation_url'] ?? '#')); ?>"
                                class="menu-link <?php if($menu['menu_class']): ?> <?php echo e($menu['menu_class']); ?> <?php endif; ?>
                                <?php if($hasSubMenu): ?> menu-toggle <?php endif; ?>">
                                <?php if($menu['menu_icon']): ?>
                                    <i class="menu-icon <?php echo e($menu['menu_icon']); ?>"></i>
                                <?php endif; ?>
                                <div><?php echo e($menu['name']); ?></div>
                            </a>

                            <?php if($hasSubMenu): ?>
                                <ul class="menu-sub">
                                    <?php $__currentLoopData = $childMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childMenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if this child menu is active
                                            $isChildActive = request()->is(ltrim($childMenu['navigation_url'], '/'));
                                        ?>
                                        <li class="menu-item <?php echo e($isChildActive ? 'active' : ''); ?>">
                                            <a href="<?php echo e(url($childMenu['navigation_url'] ?? '#')); ?>"
                                                class="menu-link <?php if($childMenu['menu_class']): ?> <?php echo e($childMenu['menu_class']); ?> <?php endif; ?>">
                                                <?php if($childMenu['menu_icon']): ?>
                                                    <i class="menu-icon <?php echo e($childMenu['menu_icon']); ?>"></i>
                                                <?php endif; ?>
                                                <div><?php echo e($childMenu['name']); ?></div>
                                            </a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            <?php endif; ?>
                        </li>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <a href="#" class="menu-horizontal-next d-none"></a>
    </div>
</aside>
<?php /**PATH C:\INCT\Parth\practice projects\RJ_ENERGY\resources\views/includes/aside.blade.php ENDPATH**/ ?>