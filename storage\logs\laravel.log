[2025-08-04 09:54:47] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'customer_bank_details' already exists (Connection: mysql, SQL: create table `customer_bank_details` (`id` bigint unsigned not null auto_increment primary key, `customer_id` bigint unsigned not null, `bank_name` varchar(255) not null, `bank_branch` varchar(255) not null, `account_number` varchar(255) not null, `ifsc_code` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'customer_bank_details' already exists (Connection: mysql, SQL: create table `customer_bank_details` (`id` bigint unsigned not null auto_increment primary key, `customer_id` bigint unsigned not null, `bank_name` varchar(255) not null, `bank_branch` varchar(255) not null, `account_number` varchar(255) not null, `ifsc_code` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `c...')
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('customer_bank_d...', Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\database\\migrations\\2025_08_02_081444_create_customer_bank_details_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_02_0814...', Object(Closure))
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_02_0814...', Object(Closure))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\INCT\\\\Parth\\\\p...', 6, false)
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'customer_bank_details' already exists at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `c...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `c...')
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('customer_bank_d...', Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\database\\migrations\\2025_08_02_081444_create_customer_bank_details_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_02_0814...', Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_02_0814...', Object(Closure))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\INCT\\\\Parth\\\\p...', 6, false)
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-04 09:55:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = iHNwQdzJsWpBEdigwYXehE5P6AlYsVCOSzLNSpTw limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = iHNwQdzJsWpBEdigwYXehE5P6AlYsVCOSzLNSpTw limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('iHNwQdzJsWpBEdi...')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('iHNwQdzJsWpBEdi...')
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('iHNwQdzJsWpBEdi...')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('iHNwQdzJsWpBEdi...')
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#48 {main}
"} 
[2025-08-04 09:55:08] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = aXDbBAnrc2yJL8R4GUpqeHgJstL2hudnmeJ0Beb7 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = aXDbBAnrc2yJL8R4GUpqeHgJstL2hudnmeJ0Beb7 limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('aXDbBAnrc2yJL8R...')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('aXDbBAnrc2yJL8R...')
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('aXDbBAnrc2yJL8R...')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('aXDbBAnrc2yJL8R...')
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#48 {main}
"} 
[2025-08-04 09:55:20] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = ssm9wdbBHegVRzmKD37Aa5Su5yD7CatW6Pmn4MID limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = ssm9wdbBHegVRzmKD37Aa5Su5yD7CatW6Pmn4MID limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('ssm9wdbBHegVRzm...')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('ssm9wdbBHegVRzm...')
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('ssm9wdbBHegVRzm...')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('ssm9wdbBHegVRzm...')
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#48 {main}
"} 
[2025-08-04 09:56:00] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = OpmDYnzhcXxwcSb5LSAzQtbZybw1Xn76TfyCfyVJ limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = OpmDYnzhcXxwcSb5LSAzQtbZybw1Xn76TfyCfyVJ limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('OpmDYnzhcXxwcSb...')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('OpmDYnzhcXxwcSb...')
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('OpmDYnzhcXxwcSb...')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('OpmDYnzhcXxwcSb...')
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#48 {main}
"} 
[2025-08-04 09:56:11] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = r40PKCGI44vDdzvYtsiajE4kGCSALMFMEIkDNlnb limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = r40PKCGI44vDdzvYtsiajE4kGCSALMFMEIkDNlnb limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('r40PKCGI44vDdzv...')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('r40PKCGI44vDdzv...')
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mydb.sessions' doesn't exist at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('r40PKCGI44vDdzv...')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('r40PKCGI44vDdzv...')
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#48 {main}
"} 
[2025-08-04 09:56:52] local.ERROR: An error occurred {"exception":"[object] (PHPOpenSourceSaver\\JWTAuth\\Exceptions\\SecretMissingException(code: 0): An error occurred at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Provider.php:47)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Lcobucci.php(76): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Provider->__construct(NULL, 'HS256', Array)
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(174): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Lcobucci->__construct(NULL, 'HS256', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(349): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(149): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->getConfigInstance(Object(Illuminate\\Foundation\\Application), 'providers.jwt')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(210): Illuminate\\Container\\Container->offsetGet('tymon.jwt.provi...')
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.manag...', Array, true)
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.manag...', Array)
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.manag...', Array)
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.manag...')
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(265): Illuminate\\Container\\Container->offsetGet('tymon.jwt.manag...')
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.auth', Array, true)
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.auth', Array)
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.auth', Array)
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.auth')
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('tymon.jwt.auth')
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('tymon.jwt.auth')
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\AuthController.php(40): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\Web\\WebAuthController.php(29): App\\Http\\Controllers\\API\\V1\\AuthController->login(Object(App\\Http\\Requests\\LoginRequest))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Web\\WebAuthController->loginPost(Object(App\\Http\\Requests\\LoginRequest))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('loginPost', Array)
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Web\\WebAuthController), 'loginPost')
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#50 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#76 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#77 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#78 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#79 {main}
"} 
[2025-08-04 09:57:14] local.ERROR: An error occurred {"exception":"[object] (PHPOpenSourceSaver\\JWTAuth\\Exceptions\\SecretMissingException(code: 0): An error occurred at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Provider.php:47)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Lcobucci.php(76): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Provider->__construct(NULL, 'HS256', Array)
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(174): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Lcobucci->__construct(NULL, 'HS256', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(349): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(149): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->getConfigInstance(Object(Illuminate\\Foundation\\Application), 'providers.jwt')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(210): Illuminate\\Container\\Container->offsetGet('tymon.jwt.provi...')
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.manag...', Array, true)
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.manag...', Array)
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.manag...', Array)
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.manag...')
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(265): Illuminate\\Container\\Container->offsetGet('tymon.jwt.manag...')
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.auth', Array, true)
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.auth', Array)
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.auth', Array)
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.auth')
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('tymon.jwt.auth')
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('tymon.jwt.auth')
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\AuthController.php(40): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\Web\\WebAuthController.php(29): App\\Http\\Controllers\\API\\V1\\AuthController->login(Object(App\\Http\\Requests\\LoginRequest))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Web\\WebAuthController->loginPost(Object(App\\Http\\Requests\\LoginRequest))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('loginPost', Array)
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Web\\WebAuthController), 'loginPost')
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#50 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#76 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#77 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#78 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#79 {main}
"} 
[2025-08-04 09:57:21] local.ERROR: An error occurred {"exception":"[object] (PHPOpenSourceSaver\\JWTAuth\\Exceptions\\SecretMissingException(code: 0): An error occurred at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Provider.php:47)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\JWT\\Lcobucci.php(76): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Provider->__construct(NULL, 'HS256', Array)
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(174): PHPOpenSourceSaver\\JWTAuth\\Providers\\JWT\\Lcobucci->__construct(NULL, 'HS256', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(349): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(149): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->getConfigInstance(Object(Illuminate\\Foundation\\Application), 'providers.jwt')
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.provi...', Array, true)
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.provi...', Array)
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.provi...', Array)
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.provi...')
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(210): Illuminate\\Container\\Container->offsetGet('tymon.jwt.provi...')
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.manag...', Array, true)
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.manag...', Array)
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.manag...', Array)
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.manag...')
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\php-open-source-saver\\jwt-auth\\src\\Providers\\AbstractServiceProvider.php(265): Illuminate\\Container\\Container->offsetGet('tymon.jwt.manag...')
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): PHPOpenSourceSaver\\JWTAuth\\Providers\\AbstractServiceProvider->PHPOpenSourceSaver\\JWTAuth\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(963): Illuminate\\Container\\Container->resolve('tymon.jwt.auth', Array, true)
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('tymon.jwt.auth', Array)
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(948): Illuminate\\Container\\Container->make('tymon.jwt.auth', Array)
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('tymon.jwt.auth')
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('tymon.jwt.auth')
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('tymon.jwt.auth')
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\AuthController.php(40): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\Web\\WebAuthController.php(29): App\\Http\\Controllers\\API\\V1\\AuthController->login(Object(App\\Http\\Requests\\LoginRequest))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Web\\WebAuthController->loginPost(Object(App\\Http\\Requests\\LoginRequest))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('loginPost', Array)
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Web\\WebAuthController), 'loginPost')
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#50 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#76 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#77 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#78 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#79 {main}
"} 
[2025-08-04 09:58:59] local.ERROR: Attempt to read property "id" on null {"userId":5,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"id\" on null at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\EmployeeDashboardController.php:737)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(289): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\INCT\\\\Parth\\\\p...', 737)
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\EmployeeDashboardController.php(737): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\INCT\\\\Parth\\\\p...', 737)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\API\\V1\\EmployeeDashboardController->fetchLeavesData(Object(Illuminate\\Http\\Request))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('fetchLeavesData', Array)
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\API\\V1\\EmployeeDashboardController), 'fetchLeavesData')
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\JwtVerify.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\JwtVerify->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#38 {main}
"} 
[2025-08-04 10:00:20] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' (Connection: mysql, SQL: select `quotations`.`id`, `customers`.`customer_name`, `customers`.`age`, `customers`.`mobile`, `customers`.`alternate_mobile`, `customers`.`aadhar`, `customers`.`pan`, `quotations`.`required`, `quotations`.`solar_capacity`, `quotations`.`rooftop_size`, `quotations`.`amount`, `quotations`.`date`, `quotations`.`by`, `quotations`.`status` from `quotations` left join `customers` on `quotations`.`customer_id` = `customers`.`id` where `quotations`.`id` = 1 limit 1) {"userId":6,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' (Connection: mysql, SQL: select `quotations`.`id`, `customers`.`customer_name`, `customers`.`age`, `customers`.`mobile`, `customers`.`alternate_mobile`, `customers`.`aadhar`, `customers`.`pan`, `quotations`.`required`, `quotations`.`solar_capacity`, `quotations`.`rooftop_size`, `quotations`.`amount`, `quotations`.`date`, `quotations`.`by`, `quotations`.`status` from `quotations` left join `customers` on `quotations`.`customer_id` = `customers`.`id` where `quotations`.`id` = 1 limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `quotati...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `quotati...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `quotati...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\QuotationController.php(100): Illuminate\\Database\\Query\\Builder->first()
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\API\\V1\\QuotationController->view(Object(Illuminate\\Http\\Request))
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('view', Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\API\\V1\\QuotationController), 'view')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\JwtVerify.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\JwtVerify->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#44 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `quotati...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `quotati...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `quotati...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `quotati...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `quotati...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\QuotationController.php(100): Illuminate\\Database\\Query\\Builder->first()
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\API\\V1\\QuotationController->view(Object(Illuminate\\Http\\Request))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('view', Array)
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\API\\V1\\QuotationController), 'view')
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\JwtVerify.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\JwtVerify->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}
"} 
[2025-08-04 10:00:26] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' (Connection: mysql, SQL: select `quotations`.`id`, `customers`.`customer_name`, `customers`.`age`, `customers`.`mobile`, `customers`.`alternate_mobile`, `customers`.`aadhar`, `customers`.`pan`, `quotations`.`required`, `quotations`.`solar_capacity`, `quotations`.`rooftop_size`, `quotations`.`amount`, `quotations`.`date`, `quotations`.`by`, `quotations`.`status` from `quotations` left join `customers` on `quotations`.`customer_id` = `customers`.`id` where `quotations`.`id` = 1 limit 1) {"userId":6,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' (Connection: mysql, SQL: select `quotations`.`id`, `customers`.`customer_name`, `customers`.`age`, `customers`.`mobile`, `customers`.`alternate_mobile`, `customers`.`aadhar`, `customers`.`pan`, `quotations`.`required`, `quotations`.`solar_capacity`, `quotations`.`rooftop_size`, `quotations`.`amount`, `quotations`.`date`, `quotations`.`by`, `quotations`.`status` from `quotations` left join `customers` on `quotations`.`customer_id` = `customers`.`id` where `quotations`.`id` = 1 limit 1) at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `quotati...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `quotati...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `quotati...', Array, true)
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\QuotationController.php(100): Illuminate\\Database\\Query\\Builder->first()
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\API\\V1\\QuotationController->view(Object(Illuminate\\Http\\Request))
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('view', Array)
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\API\\V1\\QuotationController), 'view')
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\JwtVerify.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\JwtVerify->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#44 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotations.solar_capacity' in 'field list' at C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `quotati...')
#1 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `quotati...', Array)
#2 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `quotati...', Array, Object(Closure))
#3 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `quotati...', Array, Object(Closure))
#4 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `quotati...', Array, true)
#5 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Controllers\\API\\V1\\QuotationController.php(100): Illuminate\\Database\\Query\\Builder->first()
#10 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\API\\V1\\QuotationController->view(Object(Illuminate\\Http\\Request))
#11 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('view', Array)
#12 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\API\\V1\\QuotationController), 'view')
#13 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\app\\Http\\Middleware\\JwtVerify.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\JwtVerify->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\INCT\\Parth\\practice projects\\RJ_ENERGY\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\INCT\\\\Parth\\\\p...')
#46 {main}
"} 
